# Prompt 系统架构与设计亮点

## 核心设计思路

这个 prompt 系统，主要是为了解决直接让 AI 生成 Lynx 代码时，质量不稳定、容易出错的问题。核心思路是把一个复杂的任务，拆解成几个 AI 更擅长处理的、更小的步骤。我们没有用市面上那些花里胡哨的 prompt 框架，而是自己搭了一套轻量、高效的模块化系统。

整个系统有点像一个“代码生成流水线”。我们把给 AI 的指令，分成了好几个模块，每个模块都只负责一件事，比如“核心框架约束”、“组件使用规范”、“样式系统规则”等等。最后通过一个主加载器 `ModularPromptLoader.ts` 把这些模块化的 prompt 拼装起来，形成一个完整的、给 AI 的最终指令。

这样做的好处是，每个模块都可以单独优化，而且整个 prompt 的逻辑非常清晰，后面的人接手也容易看懂。

## 设计亮点

### 1. “三段式思考”协议

这是我们自己琢磨出来的一个东西，叫 `buildStructuredProtocol`。我们发现，直接让 AI 写代码，它很容易“想到哪写到哪”，逻辑会比较乱。所以我们强制它必须遵循一个“三段式思考”的流程：

1.  **内部分析**：先别急着写，先在“脑子”里想清楚需求是什么，技术上有没有难点。
2.  **内部设计**：然后，再在“脑子”里规划一下代码的架构，用什么组件，性能怎么优化。
3.  **代码输出**：最后，把想好的东西，一口气写成完整的代码。

前面两个阶段，我们是禁止它输出任何东西的，就是为了逼它“多想少说”。从结果来看，这个方法效果很明显，生成的代码质量和结构性都好了很多。

### 2. “正向激励”与“痛苦提示”

写 prompt 其实有点像“驯兽”，你得让 AI 知道什么是“好”的，什么是“坏”的。

*   **正向激励 (`buildPositiveMotivation`)**: 我们写了一些鼓励性的话，告诉 AI 如果它遵循了我们的规范，生成的代码会有多牛逼，能达到“工业级代码质量”、“媲美顶级 APP”。这听起来有点玄学，但实际上能有效地引导 AI 往我们期望的方向走。
*   **痛苦提示 (Fallback 机制)**: 如果系统出了什么问题，或者 AI 没理解好，我们会有一个 `buildFallback` 机制，给出一个最简化、最严格的指令，明确告诉它“你必须做什么”、“绝对不能做什么”。这就像一个“安全网”，保证了最坏情况下的输出质量。

### 3. 模块化与优先级加载

整个 prompt 系统是全模块化的，每个技术点，比如 `LynxFrameworkCore`、`LynxComponents`、`TTSSStrictConstraints`，都是一个独立的 prompt 文件。

在 `buildTechnicalSpecs` 里，我们还给这些模块设置了 `CRITICAL`, `HIGH`, `MEDIUM`, `LOW` 四个优先级。这样，在拼装最终 prompt 的时候，最重要的、最核心的约束，会放在最前面，确保 AI 第一时间就能看到。这个小细节，对提升 prompt 的效果也很有帮助。

### 4. 轻量级与高性能

我们没有引入任何外部依赖，整个系统就是用最基础的 TypeScript 实现的。`ModularPromptLoader` 还用了一个简单的 `Map` 来做缓存，避免了每次都重新构建 prompt，性能上基本没有损耗。

## 待改进的地方

*   **prompt 依赖关系管理**: 目前模块间的依赖关系是靠人肉管理的，后面如果模块更多了，可能会有点乱。可以考虑做一个更自动化的依赖分析和注入机制。
*   **动态 prompt 生成**: 目前的 prompt 内容基本是静态的。未来可以考虑根据用户的不同输入，动态地组合和调整 prompt 模块，实现更智能的“个性化”代码生成。
*   **可视化调试工具**: 现在调试 prompt 基本靠看日志和猜，效率不高。可以搞一个可视化的工具，能清楚地看到每个模块加载后的 prompt 内容，以及 AI 每一步的思考过程，这样优化起来会更方便。