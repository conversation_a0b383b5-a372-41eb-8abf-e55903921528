/**
 * Canvas高级应用和绘图系统部分
 */

export const CANVAS_SYSTEM_PROMPT = `Canvas 高级应用详解（按需选择）

🔍 文档分工说明：
- 本文件：Canvas系统架构、生命周期、错误防范、API限制
- LynxCanvasAudio.ts：Canvas+Audio集成、设备像素比详细处理、可视化技术
- 两文件互补，避免重复，详细DPR处理请参考LynxCanvasAudio.ts

🚨🚨🚨 CRITICAL: Claude4 Canvas高频错误强制防范 🚨🚨🚨

**错误案例1 - 错误的Canvas初始化方式 (Claude4最常犯错误)**：
❌ 绝对禁止的错误代码：
\`\`\`javascript
// 错误！使用了错误的API
const canvas = lynx.createCanvasNG("routeCanvas");  // ❌ 错误：直接传参数
if (!canvas) return;

const ctx = canvas.getContext("2d");
const width = canvas.width;
const height = canvas.height;

// 错误！没有resize事件监听
// 错误！没有attachToCanvasView绑定
// 错误！没有pixelRatio适配

ctx.clearRect(0, 0, width, height);
ctx.fillStyle = "#f8f9fa";
ctx.fillRect(0, 0, width, height);
\`\`\`

✅ 强制要求的正确代码：
\`\`\`javascript
// 正确！使用正确的Canvas初始化流程
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    // 1. 创建未绑定的Canvas Element（无参数）
    const canvas = lynx.createCanvasNG();  // ✅ 正确：无参数创建

    // 2. 必须！设置resize事件监听（在绑定前）
    canvas.addEventListener('resize', ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      // 3. 必须！pixelRatio适配
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      console.log('Canvas setup complete, starting animation...');
      // 4. 重绘逻辑
      this.startAnimation();
    });

    // 5. 必须！绑定到Canvas View
    canvas.attachToCanvasView('canvas-llm');  // ✅ 正确：使用name属性绑定
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
},

drawSilkRoadMap(ctx, width, height) {
  // 清空画布
  ctx.clearRect(0, 0, width, height);

  // 绘制背景
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);

  // 其他绘制逻辑...
}
\`\`\`

**强制检查清单 - Canvas初始化必须包含的4个步骤**：
□ 1. lynx.krypton.createCanvasNG() - 无参数创建
□ 2. addEventListener('resize') - resize事件监听
□ 3. SystemInfo.pixelRatio - 高分屏适配
□ 4. attachToCanvasView(name) - 绑定到Canvas View

**违反后果**：缺少任何一步都会导致Canvas渲染失败、尺寸错误、模糊显示或无法显示！

🚨 CRITICAL: Lynx Canvas 查询规则
**重要提醒**：在 Lynx 框架中，Canvas 元素的查询语法与标准 Web API 不同！
- ❌ 错误：使用 CSS ID 选择器 '#canvas-id'
- ✅ 正确：使用 Lynx 专用选择器 'canvasId=canvas-id'
- 核心原因：canvas-id 属性对应 canvasId= 查询语法，这是 Lynx 框架的特有实现

Canvas 优先策略：
当选择Canvas时，应完全使用Canvas实现所有视觉渲染和交互：
- 完全使用Canvas元素绘制界面，而非DOM节点
- 禁止出现文字重叠
- 所有视觉效果、动画和交互都应在Canvas内实现
- 仅使用最少必要的view元素作为容器
- 严格限制，所有js功能全部写在canvas的执行内容里面

Canvas 渲染核心规范：

Canvas基础渲染：
- 状态管理：save()/restore()隔离，避免样式冲突
- 像素精确：devicePixelRatio适配，清晰显示
- 性能优先：局部重绘，requestAnimationFrame控制
- 内存优化：及时清理，复用对象

🚨 Canvas生命周期管理 (强制4步骤，缺一不可)：

**步骤1 - 创建Canvas Element**：
- ✅ 正确：lynx.krypton.createCanvasNG() - 无参数创建
- ❌ 错误：lynx.createCanvasNG("canvasName") - 禁止传参数
- ❌ 错误：lynx.createCanvasContext() - 已废弃API

**步骤2 - 设置resize事件监听 (必须在绑定前)**：
- ✅ 正确：canvas.addEventListener('resize', callback)
- ❌ 错误：直接使用canvas.width/canvas.height - 没有resize监听
- ❌ 错误：在attachToCanvasView后设置resize - 时机错误

**步骤3 - 设备像素比(DPR)处理 (防止模糊)**：

🔍 DPR处理的两个关键阶段：

**阶段1 - 初始化（乘以pixelRatio）**：
- ✅ 正确：canvas.width = width * SystemInfo.pixelRatio
- ✅ 正确：canvas.height = height * SystemInfo.pixelRatio
- ✅ 正确：const pixelRatio = SystemInfo.pixelRatio || 1

**阶段2 - 绘图（不乘以pixelRatio）**：
- ✅ 正确：ctx.scale(pixelRatio, pixelRatio)
- ✅ 正确：之后所有绘图操作使用逻辑尺寸（不乘pixelRatio）
- ✅ 正确：ctx.clearRect(0, 0, width, height)  // 使用逻辑尺寸
- ✅ 正确：ctx.fillRect(x, y, width, height)   // 使用逻辑尺寸
- ❌ 错误：直接使用canvas.width/canvas.height进行绘图
- ❌ 错误：绘图操作中再次乘以pixelRatio

**步骤4 - 绑定到Canvas View**：
- ✅ 正确：canvas.attachToCanvasView('canvasName')
- ❌ 错误：忘记调用attachToCanvasView - Canvas不显示
- ❌ 错误：使用错误的name参数 - 绑定失败

**生命周期管理**：
- 解绑：onUnload中调用detachFromCanvasView()和dispose()
- 资源管理：onHide暂停资源，onShow恢复资源，及时释放不用资源
- 性能优化：批量绘制，离屏渲染，资源主动释放dispose()

Lynx Three.js 支持：
\`\`\`javascript
const Three = require('@byted-lynx/three');
const window = Three.__scope; // Get the mocked globalThis to allow us to use browser api
const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 100);
const renderer = new THREE.WebGLRenderer({ canvas: new window.HTMLCanvasElement('GameCanvas') });
\`\`\`

**Canvas API限制与特性**：
- 不支持特性：roundrect、globalCompositeOperation、不规则shadow
- API限制：使用经过验证的Canvas方法
- WebGL抗锯齿：antialias和enableMSAA都为true才能启用MSAA
- 触摸事件：使用touchstart、touchmove、touchend
- 🔍 触摸坐标转换：logicalX = touch.clientX / pixelRatio（物理→逻辑）
- 设备适配：Canvas元素尺寸乘以pixelRatio，绘图操作使用逻辑尺寸
- 不使用2023年后的canvas新方法

**Canvas错误处理**：
- 创建失败：重试处理，适当延迟或requestAnimationFrame中重试
- Schema参数：添加&enable_canvas=1启用canvas扩展
- 随机ID：使用随机生成的id避免同名canvas冲突

🚨 CRITICAL: Canvas API混用致命错误防范
**绝对禁止在同一个Card中混用不同的Canvas API**：

🔥🔥🔥 **setupCanvas() 与 LightChart 绝对禁止混用** 🔥🔥🔥
- setupCanvas() 仅用于原生Canvas - 不能与LightChart混用
- initChart() 仅用于LightChart - 不能与原生Canvas混用
- 技术栈选择唯一 - 一个Card只能选择一种Canvas技术

❌ **绝对禁止的混用模式**：
- setupCanvas() + initChart() 在同一Card中
- lynx.createCanvasNG() + new LynxChart() 在同一Card中
- <canvas> + <lightcharts-canvas> 在同一TTML中
- 原生Canvas API + LightChart API 混用

✅ **正确的选择策略**：
方案A: 全部使用原生Canvas API + setupCanvas()
- setupCanvas() 方法初始化
- lynx.createCanvasNG() 创建Canvas
- canvas.getContext("2d") 获取上下文
- ctx.fillRect() 等原生绘制方法

方案B: 全部使用LightChart API + initChart()
- import LynxChart from "@byted/lynx-lightcharts/src/chart"
- initChart(e) 方法初始化
- new LynxChart() 创建图表实例
- chart.setOption() 配置图表

**🔥 强化错误检测规则 - 禁止Canvas和LightChart混用**：
如果代码中同时出现以下关键词组合，立即报错并要求重构：
- "setupCanvas" AND "initChart" - 绝对禁止在同一Card中
- "setupCanvas" AND "new LynxChart" - 绝对禁止混用
- "lynx.createCanvasNG" AND "LynxChart" - 技术栈冲突
- "lynx.createCanvasNG" AND "@byted/lynx-lightcharts" - API混用
- "getContext" AND "setOption" - 不同Canvas技术混用
- "canvas" 标签 AND "lightcharts-canvas" 标签 - TTML混用
- "attachToCanvasView" AND "bindinitchart" - 初始化方式混用

**根本原因**：不同Canvas API有不同的运行时依赖和环境要求，混用会导致：
- 图表渲染失败
- 运行时环境冲突
- 内存泄漏和性能问题

🔧 CRITICAL: Canvas创建和绑定规则（基于LynxCanvasRules.md）

**Canvas Element vs Canvas View 分离架构**：
- Canvas View: TTML中的<canvas>标签，在UI线程渲染
- Canvas Element: JS中的绘制对象，在JS线程执行
- 两者通过name属性关联，位于不同线程，松耦合关系

**正确的Canvas创建流程**：
\`\`\`javascript
// 创建未绑定的Canvas Element
const canvas = lynx.createCanvasNG();

// 监听resize事件（必须在绑定前设置）
canvas.addEventListener('resize', ({ width, height }) => {
  canvas.width = width * SystemInfo.pixelRatio;
  canvas.height = height * SystemInfo.pixelRatio;
  const ctx = canvas.getContext('2d');
  ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
  this.redraw();
});

// 重要！！！绑定到Canvas View
canvas.attachToCanvasView('canvasName');
\`\`\`


**Canvas尺寸设置关键规则**：
- Canvas View尺寸：通过TTML的style属性设置（rpx单位）
- Canvas Element尺寸：必须手动设置width/height（像素单位）
- 像素转换：rpx尺寸 × SystemInfo.pixelRatio = 像素尺寸
- 使用SelectorQuery获取Canvas View的实际尺寸：

❌ 错误的查询方式（使用CSS ID选择器）：
\`\`\`javascript
this.createSelectorQuery()
  .select('#canvasId')  // 错误：使用CSS ID选择器
  .invoke({
    method: 'boundingClientRect',
    success: (res) => {
      // res 可能为 null，因为无法找到 canvas 元素
      const pixelRatio = SystemInfo.pixelRatio || 1;
      canvas.width = res.width * pixelRatio;
      canvas.height = res.height * pixelRatio;
    }
  })
  .exec();
\`\`\`

**Canvas生命周期管理关键规则**：

1. **创建阶段**：
- 在onLoad或适当延迟后创建Canvas
- 确保Canvas View已完成排版再创建Canvas Element
- 如果创建失败，在requestAnimationFrame中重试

2. **绑定阶段**：
- resize事件监听必须在attachToCanvasView之前设置
- name属性必须全局唯一，避免冲突
- 检查getBoundingClientRect()返回值，null表示布局未完成

3. **使用阶段**：
- 监听resize事件，动态更新Canvas尺寸
- 使用save()/restore()隔离绘制状态
- 批量绘制操作，减少状态切换

4. **销毁阶段**：
\`\`\`javascript
onUnload() {
  if (this.canvas) {
    this.canvas.detachFromCanvasView();
    this.canvas.dispose();
    this.canvas = null;
  }
}
\`\`\`

**Canvas常见错误和解决方案**：


2. **Canvas创建返回null**：
- 原因：Canvas View未完成排版
- 解决：延迟创建或在requestAnimationFrame中重试

3. **多Canvas实例冲突**：
- 原因：name属性重复
- 解决：使用随机生成的唯一name

4. **息屏后Canvas白屏（Android）**：
- 原因：TextureView context被清除
- 解决：在onShow中重新设置visibility

5. **Canvas尺寸为0导致渲染异常**：
- 原因：未正确设置width/height
- 解决：确保Canvas Element尺寸设置正确

**Canvas性能优化规则**：
- 使用Canvas-NG（Krypton引擎）获得GPU线程渲染
- 复杂静态内容使用离屏Canvas预渲染
- 批量处理相同状态的绘制操作
- 及时调用dispose()释放资源
- 在onHide中暂停资源，onShow中恢复

**Canvas高级绘图系统 (原生渲染) - 参考LynxCanvasAudio.ts获取完整示例**

🔍 关键DPR处理要点：
- Canvas元素尺寸：canvas.width = width * pixelRatio（物理像素）
- 绘图操作：使用逻辑尺寸（width, height），不再乘以pixelRatio
- 触摸坐标：logicalX = touch.clientX / pixelRatio（物理→逻辑转换）

🔧 CRITICAL: Canvas 查询规则（基于 Lynx 框架特性）

**Canvas 元素查询的根本问题**：
Lynx 框架虽然借鉴了许多 Web 开发的概念，但它拥有自己独特的 API 和实现规范。一个常见的错误来源是开发者将标准的 Web API 使用习惯直接应用于 Lynx 环境，而未查阅其特定文档，从而导致兼容性问题。

**核心根源**：
开发者试图使用标准的 CSS ID 选择器（例如 #my-canvas）来查询一个需要通过 Lynx 框架特定选择器才能定位的 Canvas 组件。
在 Lynx 中，<canvas> 组件的标识符是 canvas-id，它并非 HTML 标准中的 id 属性。因此，标准的 document.querySelector('#id') 或 Lynx 提供的 query.select('#id') 语法无法正确定位到该组件。


❌ 错误示例 - 使用标准 CSS ID 选择器：
\`\`\`javascript
Page({
  onReady() {
    const query = lynx.createSelectorQuery();

    // 错误：使用 CSS ID 选择器 '#some-canvas-id'
    query.select('#some-canvas-id').fields({ node: true, size: true }).exec((res) => {
      if (res && res[0]) {
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        // ...后续绘图操作将失败，因为 canvas 为空
      } else {
        console.error("获取 Canvas 节点失败！");
      }
    });
  }
})
\`\`\`

✅ 正确示例 使用canvas.attachToCanvasView绑定到TTML的Canvas视图
**TTML结构**：
\`\`\`html
<!-- Canvas 动画区域 -->
<canvas
  name="canvas-llm"
  class="canvas-llm"
></canvas>
\`\`\`

**JavaScript初始化**：
\`\`\`javascript
// 初始化Canvas
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    // 重要：resize事件监听必须在绑定前设置
    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      console.log('Canvas setup complete, starting animation...');
      this.startAnimation();
    });

    // 绑定到Canvas视图
    canvas.attachToCanvasView("canvas-llm");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}
\`\`\`

**JavaScript初始化（精简版，详细版本参考LynxCanvasAudio.ts）**：
\`\`\`javascript
// 标准Canvas初始化流程
setupCanvas() {
  const canvas = lynx.createCanvasNG();

  canvas.addEventListener("resize", ({ width, height }) => {
    // DPR处理：阶段1-初始化（乘以pixelRatio）
    const pixelRatio = SystemInfo.pixelRatio || 1;
    canvas.width = width * pixelRatio;
    canvas.height = height * pixelRatio;

    // DPR处理：阶段2-绘图（应用缩放后使用逻辑尺寸）
    const ctx = canvas.getContext('2d');
    ctx.scale(pixelRatio, pixelRatio);

    // 保存引用（重要：保存逻辑尺寸）
    this.canvas = canvas;
    this.ctx = ctx;
    this.canvasWidth = width;   // 逻辑尺寸
    this.canvasHeight = height; // 逻辑尺寸
    this.pixelRatio = pixelRatio;

    this.startAnimation();
  });

  canvas.attachToCanvasView("canvas-name");
}
\`\`\`

**Canvas 视觉增强技术**：

**实用视觉增强**：
- 信息可视化：数据驱动的图表、图形、指示器
- 状态反馈：加载进度、操作状态、错误提示
- 导航辅助：高亮、指引、路径标识
- 内容组织：分组框架、连接线、层次标识

**精美动画效果**：
- 过渡动画：状态切换的平滑过渡，300-500ms
- 反馈动画：点击确认、悬停提示、拖拽跟随
- 引导动画：新功能介绍、操作提示
- 数据动画：图表更新、数值变化展示

**移动端优化要求**：
- 卡片样式优化：为主要指标卡片和图表背景添加圆角和渐变和高光效果
- 趋势图表增强：
  - 增加Y轴的网格线和刻度标签，使数据更易于解读
  - 优化数据点和标签的显示逻辑，禁止出现文字重叠
  - 调整图表的内边距和整体布局，使其不那么拥挤
  - 图表需要增加详细的图例说明，包含各项的名称、数值和百分比
- 动态字体大小：标题和标签的字体大小，要根据画布的宽度和高度进行计算，确保在画布缩放时文字大小能相应调整
- 最小字体限制：为字体大小设置一个最小值（12px），防止在画布过小时文字变得难以阅读
- 相对布局：标签的X、Y位置以及行高也要相对于画布尺寸和字体大小进行计算，使得整体布局更具适应性

**Canvas 开发关键点**：
- 必须显式绑定/解绑: attachToCanvasView/detachFromCanvasView
- 通过addEventListener("resize")获取实际尺寸并更新canvas宽高
- 销毁时必须解绑并清理引用
- 增加充分的 try catch 和属性 fallback 以防止兼容性错误，并打印充足的 console.log 进行 debug
- 标签使用name="canvas-llm"（不是id）
- 所有操作乘以SystemInfo.pixelRatio
- 避免新Canvas API（如roundRect和globalCompositeOperation）

🔥 **Canvas 查询规则总结**：
**永远记住**：在 Lynx 框架中进行 Canvas 查询时：
1. **TTML 中使用**：canvas-id="your-canvas-id" 属性
2. **JavaScript 查询时使用**：'canvasId=your-canvas-id' 选择器语法
3. **绝对不要使用**：'#your-canvas-id' CSS ID 选择器
4. **这是 Lynx 框架的特有实现**，与标准 Web API 不同，必须严格遵循

**记忆口诀**：canvas-id 对应 canvasId=，查询成功靠语法！

🚨🚨🚨 CLAUDE4 CANVAS错误防范总结 🚨🚨🚨

**最高频错误 - 错误的Canvas初始化模式**：
❌ Claude4经常犯的错误：
\`\`\`javascript
// 错误示例：缺少关键步骤的Canvas初始化
initRouteCanvas() {
  const canvas = lynx.createCanvasNG("routeCanvas");  // ❌ 错误1：传参数
  if (!canvas) return;

  const ctx = canvas.getContext("2d");
  const width = canvas.width;                         // ❌ 错误2：没有resize监听
  const height = canvas.height;                       // ❌ 错误3：没有pixelRatio适配

  ctx.clearRect(0, 0, width, height);                 // ❌ 错误4：没有attachToCanvasView
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);
  this.drawSilkRoadMap(ctx, width, height);
}

// 或者使用已废弃的API
const canvas = lynx.createCanvas("canvasName");       // ❌ 错误5：使用废弃API
\`\`\`

✅ 正确的Canvas初始化：
\`\`\`javascript
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      this.startAnimation();
    });

    canvas.attachToCanvasView("canvas-llm");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}
\`\`\`

✅ 强制要求的正确模式：
\`\`\`javascript
// 正确示例：完整的Canvas初始化流程
initRouteCanvas() {
  // 步骤1：创建Canvas Element（无参数）
  const canvas = lynx.krypton.createCanvasNG();

  // 步骤2：设置resize事件监听（必须在绑定前）
  canvas.addEventListener('resize', ({ width, height }) => {
    // 步骤3：pixelRatio适配（防止模糊）
    canvas.width = width * SystemInfo.pixelRatio;
    canvas.height = height * SystemInfo.pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

    // 绘制逻辑
    this.drawSilkRoadMap(ctx, width, height);
  });

  // 步骤4：绑定到Canvas View
  canvas.attachToCanvasView('routeCanvas');
},

drawSilkRoadMap(ctx, width, height) {
  ctx.clearRect(0, 0, width, height);
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);
  // 其他绘制逻辑...
}
\`\`\`

**🔥 强制检查清单 - Canvas和LightChart分离验证**：

**原生Canvas专用检查（使用setupCanvas()）**：
□ 1. lynx.createCanvasNG() - 无参数创建（不是lynx.createCanvas）
□ 2. addEventListener('resize') - resize事件监听在绑定前设置
□ 3. SystemInfo.pixelRatio - 高分屏适配处理
□ 4. attachToCanvasView(name) - 绑定到Canvas View
□ 5. 绘制逻辑在resize回调中执行
□ 6. 使用setupCanvas()方法进行初始化
□ 7. 🚨 确认没有LightChart相关代码（new LynxChart、chart.setOption等）

**LightChart专用检查（使用initChart()）**：
□ 1. import LynxChart from "@byted/lynx-lightcharts/src/chart"
□ 2. initChart(e) 方法接收事件参数
□ 3. new LynxChart({ canvasName, width, height })
□ 4. chart.setOption(option) 配置图表
□ 5. chart.destroy() 在onUnload中销毁
□ 6. <lightcharts-canvas> 标签使用
□ 7. 🚨 确认没有原生Canvas相关代码（lynx.createCanvasNG、setupCanvas等）

**混用检测（必须为空）**：
□ 8. 🔥 确认同一Card中没有setupCanvas() AND initChart()
□ 9. 🔥 确认同一Card中没有原生Canvas AND LightChart API

**违反任何一条都会导致**：
- Canvas不显示或显示异常
- 尺寸错误或模糊显示
- resize时不重绘
- 高分屏适配失败

**记住**：Canvas初始化是4步骤流程，缺一不可！`;
